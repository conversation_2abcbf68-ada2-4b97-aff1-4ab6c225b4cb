// import { Module } from '@nestjs/common';
// import { MongooseModule } from '@nestjs/mongoose';
// import { AppController } from './app.controller';
// import { AppService } from './app.service';

// @Module({
//   imports: [
//     MongooseModule.forRoot('mongodb://localhost/your-database-name'),
//   ],
//   controllers: [AppController],
//   providers: [AppService],
// })
// export class AppModule {}

// import { Module } from '@nestjs/common';
// import { MongooseModule } from '@nestjs/mongoose';
// import { AppController } from './app.controller';
// import { AppService } from './app.service';

// @Module({
//   imports: [
//     MongooseModule.forRoot('mongodb://localhost/your-database-name'),
//   ],
//   controllers: [AppController],
//   providers: [AppService],
// })
// export class AppModule {}

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // ดึงค่า URI จาก Environment Variable ที่ชื่อ 'MONGODB_URI'
        const mongoUri = configService.get<string>('MONGODB_URI');

        // เพิ่ม console.log เพื่อตรวจสอบค่า (สามารถลบออกได้เมื่อใช้งานได้แล้ว)
        console.log('MongoDB URI from .env:', mongoUri);

        // ตรวจสอบว่าได้ค่ามาหรือไม่ ก่อนที่จะส่งให้ Mongoose
        if (!mongoUri) {
          console.error('Error: MONGODB_URI is not defined in .env file or ConfigModule is not loaded correctly.');
          // คุณอาจจะโยน error หรือจัดการในรูปแบบอื่นที่นี่
          throw new Error('MongoDB URI is not configured.');
        }

        return {
          uri: mongoUri,
        };
      },
      inject: [ConfigService],
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
