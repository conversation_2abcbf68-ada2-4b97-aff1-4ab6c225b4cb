import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    MongooseModule.forRoot(
      'mongodb+srv://magnetadminapi:<EMAIL>/?retryWrites=true&w=majority&appName=magnet-admin-api',
    ),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
